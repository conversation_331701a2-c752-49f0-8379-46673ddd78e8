# Dracula PRO

Welcome to Dracula PRO. We are the **Dracula Team**, and we're here to help you _become more productive._

## 1. Choosing the right theme

As a developer, you'll spend a lot of time on your development tools. Having the right colour scheme across all apps is important to keep you focused and reduce the time it takes to switch contexts between tasks.

1. Go to the `themes` folder;
2. Pick your favourite apps;
3. Check the `INSTALL.md` for instructions on how to set it up.

## 2. Picking the right font

You type letters for a living, so it's important to have a font that feels natural and is easy on the eyes.

1. Go to the `fonts` folder;
2. Check the `INSTALL.md` for instructions on how to set it up.

## 3. Standardizing your environment

Having a unified experience across all your applications is very important to reduce the time it takes to switch context between tasks.

1. Go to the `wallpapers` folder;
2. Configure a new wallpaper for your desktop, phone, and watch.

You can also change the desktop icons to match your new wallpaper.

1. Go to the `icons` folder;
2. Pick your favourite apps;
3. Check the `INSTALL.md` for instructions on how to setup.

## 4. Adopting the right habits

You can have the best theme, font, and tooling in the world, but if you cultivate bad habits, you will not become a top developer. Because of that, I decided to reach out to the best developers I know and ask them for tips on how to be more productive.

1. Go to the `book` folder;
2. Open the `.epub` or `.mobi` file on your favourite e-book app.

Or if you prefer to listen instead of reading, check this out:

1. Go to the `audiobook` folder;
2. Pick the language you want to listen to.

Enjoy it, and let us know what you think about the book!

## Questions?

Feel free to [reach out.](mailto:<EMAIL>)

### [Gnome Terminal](https://wiki.gnome.org/Apps/Terminal)

#### Activating theme

This theme is compatible with **Gnome 3 Terminal** and any other _Gnome-based_ terminal programs like the **Unity Terminal** bundled with Ubuntu.

To get started, you'll need the `dconf` command. If you're using an Ubuntu-based distribution, you can install it by running:

```bash
sudo apt-get install dconf-cli
```

For other distros, search your repositories for **dconf** related packages to find and install the necessary package.

After installing `dconf`, choose your desired theme variant. Navigate to the folder containing the theme files and run the installation script:

```bash
./install.sh
```

#### Addons

Enhance your terminal experience by installing addons like [zsh-syntax-highlighting](https://github.com/zsh-users/zsh-syntax-highlighting/blob/master/INSTALL.md), which provides color and style highlights for commands.

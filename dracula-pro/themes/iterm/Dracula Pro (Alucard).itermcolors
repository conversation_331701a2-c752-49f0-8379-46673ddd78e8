<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Ansi 0 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.96078431606292725</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.96078431606292725</real>
		<key>Red Component</key>
		<real>0.96078431606292725</real>
	</dict>
	<key>Ansi 1 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.16470588743686676</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.22745098173618317</real>
		<key>Red Component</key>
		<real>0.79607844352722168</real>
	</dict>
	<key>Ansi 10 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.047058824449777603</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.55294120311737061</real>
		<key>Red Component</key>
		<real>0.098039217293262482</real>
	</dict>
	<key>Ansi 11 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.10196078568696976</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.51764708757400513</real>
		<key>Red Component</key>
		<real>0.61960786581039429</real>
	</dict>
	<key>Ansi 12 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.81568628549575806</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.38431373238563538</real>
		<key>Red Component</key>
		<real>0.47058823704719543</real>
	</dict>
	<key>Ansi 13 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.35294118523597717</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.094117648899555206</real>
		<key>Red Component</key>
		<real>0.74901962280273438</real>
	</dict>
	<key>Ansi 14 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.70588237047195435</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.49803921580314636</real>
		<key>Red Component</key>
		<real>0.015686275437474251</real>
	</dict>
	<key>Ansi 15 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.19215686619281769</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.16862745583057404</real>
		<key>Red Component</key>
		<real>0.17254902422428131</real>
	</dict>
	<key>Ansi 2 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.039215687662363052</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.44313725829124451</real>
		<key>Red Component</key>
		<real>0.078431375324726105</real>
	</dict>
	<key>Ansi 3 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.08235294371843338</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.43137255311012268</real>
		<key>Red Component</key>
		<real>0.51764708757400513</real>
	</dict>
	<key>Ansi 4 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.78823530673980713</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.29019609093666077</real>
		<key>Red Component</key>
		<real>0.39215686917304993</real>
	</dict>
	<key>Ansi 5 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.30196079611778259</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.078431375324726105</real>
		<key>Red Component</key>
		<real>0.63921570777893066</real>
	</dict>
	<key>Ansi 6 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.58823531866073608</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.41568627953529358</real>
		<key>Red Component</key>
		<real>0.011764706112444401</real>
	</dict>
	<key>Ansi 7 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.12156862765550613</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.12156862765550613</real>
		<key>Red Component</key>
		<real>0.12156862765550613</real>
	</dict>
	<key>Ansi 8 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>1</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>1</real>
		<key>Red Component</key>
		<real>1</real>
	</dict>
	<key>Ansi 9 Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.23921568691730499</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.29803922772407532</real>
		<key>Red Component</key>
		<real>0.84313726425170898</real>
	</dict>
	<key>Background Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.96078431606292725</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.96078431606292725</real>
		<key>Red Component</key>
		<real>0.96078431606292725</real>
	</dict>
	<key>Badge Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>0.5</real>
		<key>Blue Component</key>
		<real>0.14500156044960022</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.25274839997291565</real>
		<key>Red Component</key>
		<real>0.91916567087173462</real>
	</dict>
	<key>Bold Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.19215686619281769</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.16862745583057404</real>
		<key>Red Component</key>
		<real>0.17254902422428131</real>
	</dict>
	<key>Cursor Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.59215688705444336</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.364705890417099</real>
		<key>Red Component</key>
		<real>0.38823530077934265</real>
	</dict>
	<key>Cursor Guide Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>0.25</real>
		<key>Blue Component</key>
		<real>0.99078255891799927</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.92049425840377808</real>
		<key>Red Component</key>
		<real>0.7486235499382019</real>
	</dict>
	<key>Cursor Text Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.12156862765550613</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.12156862765550613</real>
		<key>Red Component</key>
		<real>0.12156862765550613</real>
	</dict>
	<key>Foreground Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.12156862765550613</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.12156862765550613</real>
		<key>Red Component</key>
		<real>0.12156862765550613</real>
	</dict>
	<key>Link Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.78823530673980713</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.29019609093666077</real>
		<key>Red Component</key>
		<real>0.39215686917304993</real>
	</dict>
	<key>Match Background Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.08235294371843338</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.43137255311012268</real>
		<key>Red Component</key>
		<real>0.51764708757400513</real>
	</dict>
	<key>Selected Text Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.7607843279838562</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.7764706015586853</real>
		<key>Red Component</key>
		<real>0.7764706015586853</real>
	</dict>
	<key>Selection Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.87058824300765991</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.81176471710205078</real>
		<key>Red Component</key>
		<real>0.81176471710205078</real>
	</dict>
	<key>Tab Color</key>
	<dict>
		<key>Alpha Component</key>
		<real>1</real>
		<key>Blue Component</key>
		<real>0.0</real>
		<key>Color Space</key>
		<string>P3</string>
		<key>Green Component</key>
		<real>0.0</real>
		<key>Red Component</key>
		<real>0.0</real>
	</dict>
</dict>
</plist>

runtime autoload/dracula_pro.vim

let g:dracula_pro#palette.comment   = ['#9F70A9', 139]
let g:dracula_pro#palette.selection = ['#544158',  96]

let g:dracula_pro#palette.bglighter = ['#463649',  59]
let g:dracula_pro#palette.bglight   = ['#382B3B',  59]
let g:dracula_pro#palette.bg        = ['#2A212C',  59]
let g:dracula_pro#palette.bgdark    = ['#1C161D',  53]
let g:dracula_pro#palette.bgdarker  = ['#0E0B0F',  16]

runtime colors/dracula_pro_base.vim

let g:colors_name = 'dracula_pro_buffy'

runtime autoload/dracula_pro.vim

let g:dracula_pro#palette.comment   = ['#A97079', 138]
let g:dracula_pro#palette.selection = ['#584145',  95]

let g:dracula_pro#palette.bglighter = ['#493639',  59]
let g:dracula_pro#palette.bglight   = ['#3B2B2E',  59]
let g:dracula_pro#palette.bg        = ['#2C2122',  59]
let g:dracula_pro#palette.bgdark    = ['#1D1617',  52]
let g:dracula_pro#palette.bgdarker  = ['#0F0B0B',  16]

runtime colors/dracula_pro_base.vim

let g:colors_name = 'dracula_pro_morbius'

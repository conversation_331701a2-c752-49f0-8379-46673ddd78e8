runtime autoload/dracula_pro.vim

let g:dracula_pro#palette.comment   = ['#A99F70', 144]
let g:dracula_pro#palette.selection = ['#585441', 101]

let g:dracula_pro#palette.bglighter = ['#494636',  59]
let g:dracula_pro#palette.bglight   = ['#3B382B',  59]
let g:dracula_pro#palette.bg        = ['#2C2A21',  59]
let g:dracula_pro#palette.bgdark    = ['#1D1C16',  58]
let g:dracula_pro#palette.bgdarker  = ['#0F0E0B',  16]

runtime colors/dracula_pro_base.vim

let g:colors_name = 'dracula_pro_lincoln'

runtime autoload/dracula_pro.vim

let g:dracula_pro#palette.comment   = ['#70A99F', 109]
let g:dracula_pro#palette.selection = ['#415854',  66]

let g:dracula_pro#palette.bglighter = ['#364946',  59]
let g:dracula_pro#palette.bglight   = ['#2B3B38',  59]
let g:dracula_pro#palette.bg        = ['#212C2A',  59]
let g:dracula_pro#palette.bgdark    = ['#161D1C',  23]
let g:dracula_pro#palette.bgdarker  = ['#0B0F0E',  16]

runtime colors/dracula_pro_base.vim

let g:colors_name = 'dracula_pro_blade'

runtime autoload/dracula_pro.vim

let g:dracula_pro#palette.comment   = ['#7970A9', 103]
let g:dracula_pro#palette.selection = ['#454158',  60]

let g:dracula_pro#palette.bglighter = ['#393649',  59]
let g:dracula_pro#palette.bglight   = ['#2E2B3B',  59]
let g:dracula_pro#palette.bg        = ['#22212C',  59]
let g:dracula_pro#palette.bgdark    = ['#17161D',  17]
let g:dracula_pro#palette.bgdarker  = ['#0B0B0F',  16]

runtime colors/dracula_pro_base.vim

let g:colors_name = 'dracula_pro'

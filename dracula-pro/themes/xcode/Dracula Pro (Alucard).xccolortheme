<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>DVTConsoleDebuggerInputTextColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTConsoleDebuggerInputTextFont</key>
  <string>SFMono-Bold - 14.0</string>
  <key>DVTConsoleDebuggerOutputTextColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTConsoleDebuggerOutputTextFont</key>
  <string>SFMono-Regular - 14.0</string>
  <key>DVTConsoleDebuggerPromptTextColor</key>
  <string>0.392157 0.290196 0.788235 1</string>
  <key>DVTConsoleDebuggerPromptTextFont</key>
  <string>SFMono-Regular - 14.0</string>
  <key>DVTConsoleExectuableInputTextColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTConsoleExectuableInputTextFont</key>
  <string>SFMono-Regular - 14.0</string>
  <key>DVTConsoleExectuableOutputTextColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTConsoleExectuableOutputTextFont</key>
  <string>SFMono-Bold - 14.0</string>
  <key>DVTConsoleTextBackgroundColor</key>
  <string>0.960784 0.960784 0.960784 1</string>
  <key>DVTConsoleTextInsertionPointColor</key>
  <string>0.172549 0.172549 0.172549 1</string>
  <key>DVTConsoleTextSelectionColor</key>
  <string>0.811765 0.811765 0.870588 1</string>
  <key>DVTDebuggerInstructionPointerColor</key>
  <string>0.027451 0.415686 0.588235 1</string>
  <key>DVTFontAndColorVersion</key>
  <integer>1</integer>
  <key>DVTFontSizeModifier</key>
  <integer>2</integer>
  <key>DVTLineSpacing</key>
  <real>1.1000000238418579</real>
  <key>DVTMarkupTextBackgroundColor</key>
  <string>0.882353 0.882353 0.929412 1</string>
  <key>DVTMarkupTextBorderColor</key>
  <string>0.8 0.8 0.847059 1</string>
  <key>DVTMarkupTextCodeFont</key>
  <string>SFMono-Regular - 13.0</string>
  <key>DVTMarkupTextEmphasisColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTMarkupTextEmphasisFont</key>
  <string>.SFNS-RegularItalic - 13.0</string>
  <key>DVTMarkupTextInlineCodeColor</key>
  <string>0.121569 0.121569 0.121569 0.7</string>
  <key>DVTMarkupTextLinkColor</key>
  <string>0.137255 0.411765 0.588235 1</string>
  <key>DVTMarkupTextLinkFont</key>
  <string>.SFNS-Regular - 13.0</string>
  <key>DVTMarkupTextNormalColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTMarkupTextNormalFont</key>
  <string>.SFNS-Regular - 13.0</string>
  <key>DVTMarkupTextOtherHeadingColor</key>
  <string>0.121569 0.121569 0.121569 0.5</string>
  <key>DVTMarkupTextOtherHeadingFont</key>
  <string>.SFNS-Regular - 18.2</string>
  <key>DVTMarkupTextPrimaryHeadingColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTMarkupTextPrimaryHeadingFont</key>
  <string>.SFNS-Regular - 31.2</string>
  <key>DVTMarkupTextSecondaryHeadingColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTMarkupTextSecondaryHeadingFont</key>
  <string>.SFNS-Regular - 23.4</string>
  <key>DVTMarkupTextStrongColor</key>
  <string>0.121569 0.121569 0.121569 1</string>
  <key>DVTMarkupTextStrongFont</key>
  <string>.SFNS-Bold - 13.0</string>
  <key>DVTScrollbarMarkerAnalyzerColor</key>
  <string>0.392157 0.290196 0.788235 1</string>
  <key>DVTScrollbarMarkerBreakpointColor</key>
  <string>0.792157 0.301961 0.082353 1</string>
  <key>DVTScrollbarMarkerDiffColor</key>
  <string>0.388235 0.372549 0.592157 1</string>
  <key>DVTScrollbarMarkerDiffConflictColor</key>
  <string>0.792157 0.301961 0.082353 1</string>
  <key>DVTScrollbarMarkerErrorColor</key>
  <string>0.792157 0.301961 0.082353 1</string>
  <key>DVTScrollbarMarkerRuntimeIssueColor</key>
  <string>0.392157 0.290196 0.788235 1</string>
  <key>DVTScrollbarMarkerWarningColor</key>
  <string>0.635294 0.364706 0.082353 1</string>
  <key>DVTSourceTextBackground</key>
  <string>0.960784 0.960784 0.960784 1</string>
  <key>DVTSourceTextBlockDimBackgroundColor</key>
  <string>0.501961 0.501961 0.501961 1</string>
  <key>DVTSourceTextCurrentLineHighlightColor</key>
  <string>0.882353 0.882353 0.929412 1</string>
  <key>DVTSourceTextInsertionPointColor</key>
  <string>0.172549 0.172549 0.172549 1</string>
  <key>DVTSourceTextInvisiblesColor</key>
  <string>0.388235 0.364706 0.592157 1</string>
  <key>DVTSourceTextSelectionColor</key>
  <string>0.811765 0.811765 0.870588 1</string>
  <key>DVTSourceTextSyntaxColors</key>
  <dict>
    <key>xcode.syntax.attribute</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.character</key>
    <string>0.392157 0.290196 0.788235 1</string>
    <key>xcode.syntax.comment</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.comment.doc</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.comment.doc.keyword</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.declaration.other</key>
    <string>0.054902 0.278431 0.090196 1</string>
    <key>xcode.syntax.declaration.type</key>
    <string>0.027451 0.415686 0.588235 1</string>
    <key>xcode.syntax.identifier.class</key>
    <string>0.054902 0.278431 0.090196 1</string>
    <key>xcode.syntax.identifier.class.system</key>
    <string>0.796078 0.227451 0.168627 1</string>
    <key>xcode.syntax.identifier.constant</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.identifier.constant.system</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.identifier.function</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.identifier.function.system</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.identifier.macro</key>
    <string>0.635294 0.364706 0.082353 1</string>
    <key>xcode.syntax.identifier.macro.system</key>
    <string>0.635294 0.364706 0.082353 1</string>
    <key>xcode.syntax.identifier.type</key>
    <string>0.054902 0.278431 0.090196 1</string>
    <key>xcode.syntax.identifier.type.system</key>
    <string>0.796078 0.227451 0.168627 1</string>
    <key>xcode.syntax.identifier.variable</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.identifier.variable.system</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.keyword</key>
    <string>0.635294 0.082353 0.301961 1</string>
    <key>xcode.syntax.mark</key>
    <string>0.388235 0.364706 0.592157 1</string>
    <key>xcode.syntax.markup.code</key>
    <string>0.635294 0.082353 0.301961 1</string>
    <key>xcode.syntax.number</key>
    <string>0.392157 0.290196 0.788235 1</string>
    <key>xcode.syntax.plain</key>
    <string>0.121569 0.121569 0.121569 1</string>
    <key>xcode.syntax.preprocessor</key>
    <string>0.635294 0.364706 0.082353 1</string>
    <key>xcode.syntax.string</key>
    <string>0.517647 0.431373 0.082353 1</string>
    <key>xcode.syntax.url</key>
    <string>0.392157 0.290196 0.788235 1</string>
  </dict>
  <key>DVTSourceTextSyntaxFonts</key>
  <dict>
    <key>xcode.syntax.attribute</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.character</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.comment</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.comment.doc</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.comment.doc.keyword</key>
    <string>SFMono-Bold - 14.0</string>
    <key>xcode.syntax.declaration.other</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.declaration.type</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.class</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.class.system</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.constant</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.identifier.constant.system</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.identifier.function</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.identifier.function.system</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.identifier.macro</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.macro.system</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.type</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.type.system</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.identifier.variable</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.identifier.variable.system</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.keyword</key>
    <string>SFMono-Bold - 14.0</string>
    <key>xcode.syntax.mark</key>
    <string>SFMono-Bold - 14.0</string>
    <key>xcode.syntax.markup.code</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.number</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.plain</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.preprocessor</key>
    <string>SFMono-Medium - 14.0</string>
    <key>xcode.syntax.string</key>
    <string>SFMono-Regular - 14.0</string>
    <key>xcode.syntax.url</key>
    <string>SFMono-Regular - 14.0</string>
  </dict>
</dict>
</plist>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>DVTConsoleDebuggerInputTextColor</key>
	<string>0.972549 0.972549 0.94902 1</string>
	<key>DVTConsoleDebuggerInputTextFont</key>
	<string>SFMono-Bold - 14.0</string>
	<key>DVTConsoleDebuggerOutputTextColor</key>
	<string>0.972549 0.972549 0.94902 1</string>
	<key>DVTConsoleDebuggerOutputTextFont</key>
	<string>SFMono-Regular - 14.0</string>
	<key>DVTConsoleDebuggerPromptTextColor</key>
	<string>1 0.584314 0.501961 1</string>
	<key>DVTConsoleDebuggerPromptTextFont</key>
	<string>SFMono-Regular - 14.0</string>
	<key>DVTConsoleExectuableInputTextColor</key>
	<string>0.972549 0.972549 0.94902 1</string>
	<key>DVTConsoleExectuableInputTextFont</key>
	<string>SFMono-Regular - 14.0</string>
	<key>DVTConsoleExectuableOutputTextColor</key>
	<string>0.972549 0.972549 0.94902 1</string>
	<key>DVTConsoleExectuableOutputTextFont</key>
	<string>SFMono-Bold - 14.0</string>
	<key>DVTConsoleTextBackgroundColor</key>
	<string>0.172549 0.129412 0.133333 1</string>
	<key>DVTConsoleTextInsertionPointColor</key>
	<string>1 1 1 1</string>
	<key>DVTConsoleTextSelectionColor</key>
	<string>0.345098 0.254902 0.270588 1</string>
	<key>DVTDebuggerInstructionPointerColor</key>
	<string>1 0.584314 0.501961 1</string>
	<key>DVTFontAndColorVersion</key>
	<integer>1</integer>
	<key>DVTFontSizeModifier</key>
	<integer>-5</integer>
	<key>DVTLineSpacing</key>
	<real>1.1000000238418579</real>
	<key>DVTMarkupTextBackgroundColor</key>
	<string>0.188245 0.192381 0.226996 1</string>
	<key>DVTMarkupTextBorderColor</key>
	<string>0.253186 0.25699 0.288836 1</string>
	<key>DVTMarkupTextCodeFont</key>
	<string>SFMono-Regular - 13.0</string>
	<key>DVTMarkupTextEmphasisColor</key>
	<string>0.877929 0.872979 0.878041 1</string>
	<key>DVTMarkupTextEmphasisFont</key>
	<string>.SFNS-RegularItalic - 13.0</string>
	<key>DVTMarkupTextInlineCodeColor</key>
	<string>0.877929 0.872979 0.878041 0.7</string>
	<key>DVTMarkupTextLinkColor</key>
	<string>0.384314 0.447059 0.643137 1</string>
	<key>DVTMarkupTextLinkFont</key>
	<string>.SFNS-Regular - 13.0</string>
	<key>DVTMarkupTextNormalColor</key>
	<string>0.877929 0.872979 0.878041 1</string>
	<key>DVTMarkupTextNormalFont</key>
	<string>.SFNS-Regular - 13.0</string>
	<key>DVTMarkupTextOtherHeadingColor</key>
	<string>0.877929 0.872979 0.878041 0.5</string>
	<key>DVTMarkupTextOtherHeadingFont</key>
	<string>.SFNS-Regular - 18.2</string>
	<key>DVTMarkupTextPrimaryHeadingColor</key>
	<string>0.877929 0.872979 0.878041 1</string>
	<key>DVTMarkupTextPrimaryHeadingFont</key>
	<string>.SFNS-Regular - 31.2</string>
	<key>DVTMarkupTextSecondaryHeadingColor</key>
	<string>0.877929 0.872979 0.878041 1</string>
	<key>DVTMarkupTextSecondaryHeadingFont</key>
	<string>.SFNS-Regular - 23.4</string>
	<key>DVTMarkupTextStrongColor</key>
	<string>0.877929 0.872979 0.878041 1</string>
	<key>DVTMarkupTextStrongFont</key>
	<string>.SFNS-Bold - 13.0</string>
	<key>DVTScrollbarMarkerAnalyzerColor</key>
	<string>0.403922 0.372549 1 1</string>
	<key>DVTScrollbarMarkerBreakpointColor</key>
	<string>0.290196 0.290196 0.968627 1</string>
	<key>DVTScrollbarMarkerDiffColor</key>
	<string>0.556863 0.556863 0.556863 1</string>
	<key>DVTScrollbarMarkerDiffConflictColor</key>
	<string>0.968627 0.290196 0.290196 1</string>
	<key>DVTScrollbarMarkerErrorColor</key>
	<string>0.968627 0.290196 0.290196 1</string>
	<key>DVTScrollbarMarkerRuntimeIssueColor</key>
	<string>0.643137 0.509804 1 1</string>
	<key>DVTScrollbarMarkerWarningColor</key>
	<string>0.937255 0.717647 0.34902 1</string>
	<key>DVTSourceTextBackground</key>
	<string>0.172549 0.129412 0.133333 1</string>
	<key>DVTSourceTextBlockDimBackgroundColor</key>
	<string>0.5 0.5 0.5 1</string>
	<key>DVTSourceTextCurrentLineHighlightColor</key>
	<string>0.231373 0.172549 0.176471 1</string>
	<key>DVTSourceTextInsertionPointColor</key>
	<string>1 1 1 1</string>
	<key>DVTSourceTextInvisiblesColor</key>
	<string>0.496379 0.5 0.5 1</string>
	<key>DVTSourceTextSelectionColor</key>
	<string>0.345098 0.254902 0.270588 1</string>
	<key>DVTSourceTextSyntaxColors</key>
	<dict>
		<key>xcode.syntax.attribute</key>
		<string>0.87451 0.772549 0.623529 1</string>
		<key>xcode.syntax.character</key>
		<string>0.584314 0.501961 1 1</string>
		<key>xcode.syntax.comment</key>
		<string>0.662745 0.439216 0.47451 1</string>
		<key>xcode.syntax.comment.doc</key>
		<string>0.662745 0.439216 0.47451 1</string>
		<key>xcode.syntax.comment.doc.keyword</key>
		<string>0.662745 0.439216 0.47451 1</string>
		<key>xcode.syntax.declaration.other</key>
		<string>0.623529 0.87451 0.831373 1</string>
		<key>xcode.syntax.declaration.type</key>
		<string>0.501961 1 0.917647 1</string>
		<key>xcode.syntax.identifier.class</key>
		<string>0.541176 1 0.501961 1</string>
		<key>xcode.syntax.identifier.class.system</key>
		<string>1 0.584314 0.501961 1</string>
		<key>xcode.syntax.identifier.constant</key>
		<string>0.647059 0.87451 0.623529 1</string>
		<key>xcode.syntax.identifier.constant.system</key>
		<string>0.87451 0.666667 0.623529 1</string>
		<key>xcode.syntax.identifier.function</key>
		<string>0.647059 0.87451 0.623529 1</string>
		<key>xcode.syntax.identifier.function.system</key>
		<string>0.87451 0.666667 0.623529 1</string>
		<key>xcode.syntax.identifier.macro</key>
		<string>1 0.792157 0.501961 1</string>
		<key>xcode.syntax.identifier.macro.system</key>
		<string>1 0.792157 0.501961 1</string>
		<key>xcode.syntax.identifier.type</key>
		<string>0.541176 1 0.501961 1</string>
		<key>xcode.syntax.identifier.type.system</key>
		<string>1 0.584314 0.501961 1</string>
		<key>xcode.syntax.identifier.variable</key>
		<string>0.647059 0.87451 0.623529 1</string>
		<key>xcode.syntax.identifier.variable.system</key>
		<string>0.87451 0.666667 0.623529 1</string>
		<key>xcode.syntax.keyword</key>
		<string>1 0.501961 0.74902 1</string>
		<key>xcode.syntax.mark</key>
		<string>0.662745 0.439216 0.47451 1</string>
		<key>xcode.syntax.markup.code</key>
		<string>1 0.501961 0.74902 1</string>
		<key>xcode.syntax.number</key>
		<string>0.584314 0.501961 1 1</string>
		<key>xcode.syntax.plain</key>
		<string>0.965416 0.966618 0.933125 1</string>
		<key>xcode.syntax.preprocessor</key>
		<string>1 0.792157 0.501961 1</string>
		<key>xcode.syntax.string</key>
		<string>1 1 0.501961 1</string>
		<key>xcode.syntax.url</key>
		<string>0.584314 0.501961 1 1</string>
	</dict>
	<key>DVTSourceTextSyntaxFonts</key>
	<dict>
		<key>xcode.syntax.attribute</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.character</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.comment</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.comment.doc</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.comment.doc.keyword</key>
		<string>SFMono-Bold - 14.0</string>
		<key>xcode.syntax.declaration.other</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.declaration.type</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.class</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.class.system</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.constant</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.identifier.constant.system</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.identifier.function</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.identifier.function.system</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.identifier.macro</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.macro.system</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.type</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.type.system</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.identifier.variable</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.identifier.variable.system</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.keyword</key>
		<string>SFMono-Bold - 14.0</string>
		<key>xcode.syntax.mark</key>
		<string>SFMono-Bold - 14.0</string>
		<key>xcode.syntax.markup.code</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.number</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.plain</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.preprocessor</key>
		<string>SFMono-Medium - 14.0</string>
		<key>xcode.syntax.string</key>
		<string>SFMono-Regular - 14.0</string>
		<key>xcode.syntax.url</key>
		<string>SFMono-Regular - 14.0</string>
	</dict>
</dict>
</plist>

return {
  {
    -- Dracula Pro theme support
    -- This adds Dracula Pro colorschemes as an option without interfering with NvChad themes
    dir = vim.fn.stdpath("data") .. "/site/pack/themes/start/dracula_pro",
    name = "dracula_pro",
    lazy = true, -- Load only when explicitly called
    config = function()
      -- Optional: Configure Dracula Pro settings
      -- These settings will only apply when using Dracula Pro themes
      vim.g.dracula_bold = 1
      vim.g.dracula_italic = 1
      vim.g.dracula_strikethrough = 1
      vim.g.dracula_underline = 1
      vim.g.dracula_undercurl = 1
      vim.g.dracula_full_special_attrs_support = 1
      vim.g.dracula_high_contrast_diff = 0
      vim.g.dracula_inverse = 1
      vim.g.dracula_colorterm = 0
    end,
  },
}
